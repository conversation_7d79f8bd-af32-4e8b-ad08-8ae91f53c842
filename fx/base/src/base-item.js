export class BS_ITEM {
    static changesMap = new Map();
    #fnListen = (e, isSet = true) => {
        if (this.isReady || this.isNew) {
            let isReturn = false;
            e.size === 1 && e.forEach((v, k) => {
                isReturn = k.startsWith('$');
                isReturn ||= k.startsWith('expanded');
                isReturn ||= k.startsWith('checked');
            })
            // console.log('... changed BS_ITEM ... ', this.label, e || '', this._id, 'RETURN - ', isReturn);
            if (isReturn) return;
            if (isSet && !this._id.startsWith('item:'))
                BS_ITEM.changesMap.set(this._id, this);
        }
    }

    constructor(args = {}) {
        let $doc = args.doc || {},
            lzs = FX.zduri(($doc.lzs || ''));
        $doc = lzs ? lzs = JSON.parse(lzs) : $doc;
        $doc.type ||= args.type || 'item';
        let ulid = $doc.ulid || args.ulid || FX.ulid();
        $doc._id ||= args._id || $doc.type + ':' + ulid;
        // $doc.date_utc ||= args.date || args.utc || args.date_utc || FX.dates(new Date()).utc;
        //$doc.date_local ||= FX.dates(new Date($doc.date_utc)).local;
        $doc.label ||= args.label || '';
        if (args.newLabel) $doc.label = '...'; // 'new ' + $doc.type;
        $doc.parentId ||= args.parentId;
        // this.items = [];
        this.doc = FX.icaro({ ...$doc });
        this.doc = FX.icaroListen(this.doc, this.#fnListen, 'oneFnItem');
        Object.keys(args.props || {}).forEach(key => this[key] = args.props[key]);
        this.isNew = args.isLoad ? false : !!!$doc._rev;
        if (this.isNew)
            this.#fnListen(`add new ${this.type + (this.doc.cell_type ? '_' + this.doc.cell_type : '')} - `);
        setTimeout(() => {
            this.isReady = true;
        }, 1000)
    }
    get _id() { return this.doc._id }
    get _rev() { return this.doc._rev }
    get name() { return this.doc._id.replaceAll(':', '_') }
    get ulid() { return this._id.split(':').at(-1) }
    get date_utc() { return FX.ulidToUTC(this.ulid) }
    get date_local() { return FX.ulidToLocal(this.ulid) }
    get type() { return this.doc.type }
    get label() { return this.doc.label }
    set label(v) { this.doc.label = v }
    get icon() { return this.doc.icon }
    set icon(v) { this.doc.icon = v }
    get iconFill() { return this.doc.iconFill }
    set iconFill(v) { this.doc.iconFill = v }
    get is() { return this.doc.is }
    set is(v) { this.doc.is = v }
    get if() { return this.doc.if }
    set if(v) { this.doc.if = v }
    get parentId() { return this.doc.parentId }
    set parentId(v) { this.doc.parentId = v }
    get _deleted() { return this.doc._deleted }
    set _deleted(v) {
        if (v) {
            this.doc._deleted = true;
        } else {
            delete this.doc._deleted;
            this.#fnListen('delete doc._deleted');
        }
    }
    get changed() { return BS_ITEM.changesMap.has(this._id) }
    io() { FX.IO(this) }
    iod() { FX.IO(this.doc) }
    jsd() { FX.showJSON(this.doc) }
    get lzsDoc() {
        const lzs = FX.jcuri(this.doc);
        return { _id: this._id, lzs };
    }
}

export class BS_UTILS {
    expandFromRoot(item, collapsedOther) {
        let parent = item.parent,
            _parent = parent;
        while (parent) {
            parent.expanded = true;
            if (parent.parent) _parent = parent.parent;
            parent = parent.parent;
        }
        if (collapsedOther) {
            this.setItemsProp(_parent, 'expanded', false);
            parent = item.parent;
            while (parent) {
                parent.expanded = true;
                parent = parent.parent;
            }
        }
    }
    setItemsProp(item, prop, val) {
        if (!item || !prop) return;
        const arr = item.items?.length ? item.items : item.length ? item : [];
        if (arr?.forEach) {
            arr.forEach(i => {
                this.setItemsProp(i, prop, val);
            })
        }
        item[prop] = val;
    }
    flatItems(item = {}, addItem = false) {
        const flat = {};
        if (addItem)
            flat[item._id || item.id || 'root'] = item;
        const fn = (item) => (item?.items || []).map(i => {
            i.parentId = item._id || item.id;
            flat[i._id || i.id] = i;
            fn(i);
        })
        fn(item);
        return flat;
    }
    allItems(item = {}, idOnly = false) {
        const all = [idOnly ? item._id || item.id || 'root' : item];
        const fn = (item, idOnly) => (item?.items || []).map(i => {
            all.push(idOnly ? i._id || i.id : i);
            fn(i, idOnly);
        })
        fn(item, idOnly);
        return all;
    }
    toDelete(item = {}, idOnly = false) {
        const all = [];
        if (item._deleted)
            all.push(idOnly ? item._id || item.id : item);
        const fn = (item, idOnly) => (item?.items || []).map(i => {
            if (i._deleted)
                all.push(idOnly ? i._id || i.id : i);
            fn(i, idOnly);
        })
        fn(item, idOnly);
        return all;
    }
}

globalThis.BS_UTILS ||= new BS_UTILS();
