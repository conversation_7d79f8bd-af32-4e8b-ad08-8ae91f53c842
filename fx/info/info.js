import { FxElement, html, css } from '../../fx.js';
import '../button/button.js';

customElements.define('fx-info', class FxInfo extends FxElement {
    static properties = {
        id: { type: String, default: '' }
    }

    async linkClick(e) {
        e.stopPropagation();
        const base = FX.fxBase;
        if (!base || !base.fxSelected) return;
        if (!this.id) {
            this.id ||= 'info:' + base.fxSelected._id + ':table-row:' + FX.ulid();
            this.fire('changeInfo', this.id);
        }
        let _id = this.id;
        await base.getInfo(_id, base.dbLocal, 'addNotebook');
        this.async(() => {
            base.main.fxTabs.selectTab('', 'add-info', true);
        }, 100)
        this.$update();
    }

    static styles = css`

    `

    render() {
        return html`
            <fx-icon class="pointer" name="emojione-v1:circled-information-source" @click=${this.linkClick} style="opacity: ${this.id ? .6 : .1}"></fx-icon>
        `
    }
})
