import { FxElement, html } from '/fx.js';

import '../jtmp/jtmp.js';

const distPath = import.meta.url.split('/').slice(0, -1).join('/') + '/dist/';

customElements.define('fx-jhtml', class FxJHTML extends FxElement {
    static properties = {
        src: { type: String },
        url: { type: String },
        cell: { type: Object },
        editMode: { type: Boolean, notify: true },
        isEditorOnly: { default: false }
    }
    get tmp() { return this.$qs('fx-jtmp') };
    get isAttachments() { return this.cell?.attachment || this.cell?.attachments }

    firstUpdated() {
        super.firstUpdated();
        this.listen('embed-images', async (e) => {
            FX.fxBase.showLoader = true;
            await this.embedImages();
        })
        this.async(async () => {
            await this.init();
        }, 20)
    }

    async 'editMode-changed'(e) {
        if (this.isEditorOnly) return;
        if (this.editMode) {
            const blob = await this.getBlob();
            if (blob) {
                this._src = this.src = await blob.text();
                this.$update();
            }
        }
    }
    async getBlob() {
        let db = this.db = FX.fxBase.dbLocal,
            _id = this.base.idInfo,
            doc, blob;
        try { doc = await db.getAttachment(_id, this.cell.ulid) } catch (error) { }
        if (!doc) {
            _id = this._idFile = this.cell?._idFile || 'file:' + this.base.bsSelected._id + '-' + this.cell.ulid;
            try { doc = await db.getAttachment(_id, 'file') } catch (error) { }
        }
        if (doc) {
            const arrayBuffer = await doc.arrayBuffer();
            blob = new Blob([arrayBuffer], { type: doc.type });
        }
        return blob
    }

    async init() {
        if (this.isEditorOnly) return;
        if (this.isAttachments) {
            const blob = await this.getBlob();
            if (blob) {
                this.url = URL.createObjectURL(blob, { type: 'data:text' });
                this.$update();
                this.setCheckedURL();
            }
        } else {
            this.src = this.cell?.source || '';
            this.setCheckedURL('', this.src);
        }
        this.$update();
    }

    async setCheckedURL(originalBlobUrl = this.url, text = '') {
        const response = await fetch(originalBlobUrl);
        const originalBlob = await response.blob();
        text ||= await originalBlob.text();
        const styledContent = `
<!DOCTYPE html>
<meta charset="utf-8">
            ${text}
        `;
        const newBlob = new Blob([styledContent], { type: 'text/html' });
        this.checkedURL = URL.createObjectURL(newBlob);
    }

    async saveAttachment(src = '') {
        if (this.isEditorOnly) return;
        let nb = 'notebook';
        if (FX.fxBase.main.fxTabs.activeTabName === 'add-info') {
            nb = 'addNotebook';
        }
        this._src = src;
        let blob = new Blob([src], { type: 'text/html' });
        this.url = URL.createObjectURL(blob);
        this.setCheckedURL();
        if (this.cell) {
            this.cell.size = blob.size;
            this.cell.attachment = this.ulid;
            FX.fxBase[nb]._attachments ||= {};
            FX.fxBase[nb]._attachments[this.cell.ulid] = {
                content_type: blob.type,
                data: blob
            }
        }
        this.$update();
    }

    render() {
        return html`
            ${this.editMode || this.isEditorOnly ? html`
                <fx-jtmp id="editor" .srcdoc=${this.srcdoc} src=${this.src} editMode .saveAttachment=${this.isEditorOnly ? null : this.saveAttachment.bind(this)}></fx-jtmp> 
            ` : this.url ? html`
                <iframe .src=${this.url || ''} style="width: 100%; height: 100%; border: none; overflow: auto; min-height: 0px;"></iframe>
            ` : html`
                <iframe .srcdoc=${this.src || ''} style="width: 100%; height: 100%; border: none; overflow: auto; min-height: 0px;"></iframe>
            `}
        `
    }

    embedImages() {
        console.log('embed-images');
        let src = this._src || this.src || '';
        if (!src) return;
        const imgRegex = /<img[^>]+src="([^"]+)"[^>]*>/g;
        let match;
        let promises = [];
        while ((match = imgRegex.exec(src)) !== null) {
            const imgUrl = match[1];
            if (imgUrl.startsWith('data:')) continue;
            const promise = new Promise((resolve) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);
                    const dataUrl = canvas.toDataURL();
                    this.compressImage(dataUrl, imgUrl, (compressedDataUrl) => {
                        src = src.replace(imgUrl, compressedDataUrl);
                        resolve();
                    })
                }
                img.onerror = () => {
                    console.error('Error load image:', imgUrl);
                    resolve();
                }
                img.src = imgUrl;
            })
            promises.push(promise);
        }
        Promise.all(promises).then(() => {
            this.saveAttachment(src);
            FX.fxBase.showLoader = false;
            console.log('Embed images complete')
            this.$update();
        })
    }
    async compressImage(dataUrl, originalUrl, callback) {
        await import('/fx/~/compressorjs/compressor.js');
        const base64Data = dataUrl.split(',')[1];
        const mimeType = dataUrl.split(',')[0].split(':')[1].split(';')[0];
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        const blob = new Blob([bytes], { type: mimeType });
        const fileName = originalUrl.split('/').pop() || 'image.jpg';
        const file = new File([blob], fileName, { type: mimeType });
        const options = {
            quality: 0.7,
            maxWidth: 1920,
            maxHeight: 1920,
            success: (result) => {
                const reader = new FileReader();
                reader.readAsDataURL(result);
                reader.onload = () => {
                    const compressedDataUrl = reader.result;
                    console.log('Size after compression:', result.size);
                    console.log('Compression ratio:', ((file.size - result.size) / file.size * 100).toFixed(2) + '%');
                    callback(compressedDataUrl);
                };
            },
            error: (msg) => {
                console.error('Error compress image:', msg);
                callback(dataUrl);
            }
        }
        new Compressor(file, options);
    }

    srcdoc(v, h) {
        return `
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script src="${distPath}jquery-3.5.1.min.js"></script>
    <script src="${distPath}popper.min.js"></script>

    <link rel="stylesheet" href="${distPath}bootstrap.min.css">
    <script src="${distPath}bootstrap.min.js"></script>

    <link href="${distPath}summernote-bs4.min.css" rel="stylesheet">
    <script src="${distPath}summernote-bs4.min.js"></script>
    <style> 
        ::-webkit-scrollbar { width: 4px; height: 4px; }
        ::-webkit-scrollbar-track { background: lightgray; }
        ::-webkit-scrollbar-thumb { background-color: #aaa; }
        *, *:before, *:after {  box-sizing: border-box; }
        .note-editor {
            border: none !important;
        }
        .note-toolbar {
            background: lightgray;
            margin: 2px;
            position: sticky;
            top: 0;
            z-index: 999;
        }
        .note-statusbar {
            display: none;
        }
        .note-codable {
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="summernote">${v}</div>
    <script type="module">
        var embedImages = function (context) {
            var ui = $.summernote.ui;
            var button = ui.button({
                contents: 'embed',
                tooltip: 'embed Images',
                click: function (e) {
                    document.dispatchEvent(new CustomEvent("change", { detail: { type: 'no-change', fire: 'embed-images' } }));
                }
            })
            return button.render();
        }

        $('#summernote').summernote({
            placeholder: '',
            tabsize: 2,
            height: ${h || undefined},
            focus: true,
            minHeight: '100%',
            toolbar: [
                ['style', ['style']],
                ['fontsize', ['fontsize']],
                ['fontname', ['fontname']],
                ['color', ['color']],
                ['font', ['bold', 'italic', 'underline', 'clear']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['insert', ['link', 'picture', 'video']],
                ['table', ['table']],
                ['hr', ['hr']],
                ['font', ['strikethrough', 'superscript', 'subscript']],
                ['height', ['height']],
                // ['view', ['fullscreen', 'codeview', 'help']]
                ['view', ['codeview', 'help']],
                ['embed', ['embedImages']]
            ],
            buttons: {
                embedImages: embedImages
            },
            callbacks: {
                onChange: function(contents, $editable) {
                  document.dispatchEvent(new CustomEvent("change", { detail: contents }));
                }
            }
        })
    </script>
</body>
</html>
        `
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

// const usedIcons =
// {

// }
// FX.setIcons(usedIcons);
