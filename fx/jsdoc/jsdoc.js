import { FxElement, html, css } from '/fx.js';

import '../jtmp/jtmp.js';

const libPath = import.meta.url.split('/').slice(0, -1).join('/') + '/lib/';

customElements.define('fx-jsdoc', class FxJsdoc extends FxElement {
    static properties = {
        editMode: { type: Boolean, notify: true },
        src: { type: String },
        url: { type: String, default: '', notify: true },
        checkedURL: { type: String, default: '' },
        cell: { type: Object }
    }
    get iframe() { return this.$qs('iframe') }
    get isAttachments() { return this.cell?.attachment || this.cell?.attachments }

    constructor() {
        super();
        this._handleWindowResize = this._setIframe.bind(this);
        this._handleChange = this._onChange.bind(this);
    }
    connectedCallback() {
        super.connectedCallback();
        window.addEventListener('resize', this._handleWindowResize);
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        window.removeEventListener('resize', this._handleWindowResize);
    }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this.isReady = true;
            this.init();
        })
    }

    'url-changed'(e) {
        if (!e) return;
        if (this.isReady && !this.isAttachments && !this.isLoadFile && this._lastUrl !== e) {
            this._lastUrl = e;
            this.checkedURL = this.url = FX.checkUrl(e);
            this.setIframe();
            this.$update();
        }
    }
    'editMode-changed'(e) {
        this.setIframe();
        this.$update();
    }

    _onChange(e) {
        this.saveAttachment(e.detail);
    }

    async init() {
        if (this.isAttachments) {
            const blob = await this.getBlob();
            if (blob)
                this.checkedURL = URL.createObjectURL(blob);
        } else {
            let url = this.cell?.source || this.cell?.url || this.url;
            this.checkedURL = this.url = FX.checkUrl(url);
            this.$update();
        }
        this.setIframe();
        this.$update();
    }
    loadFile(file) {
        if (file) {
            this.async(() => {
                this.checkedURL = URL.createObjectURL(file);
                this.setIframe();
                this.saveAttachment(file);
            })
        }
    }
    saveAttachment(blob) {
        let nb = 'notebook';
        if (FX.fxBase.main.fxTabs.activeTabName === 'add-info') {
            nb = 'addNotebook';
        }
        this.checkedURL = URL.createObjectURL(blob);
        if (this.cell) {
            this.cell.size = blob.size;
            this.cell.attachment = this.ulid;
            if (blob.name)
                this.cell.url = blob.name;
            FX.fxBase[nb]._attachments ||= {};
            FX.fxBase[nb]._attachments[this.cell.ulid] = {
                content_type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                data: blob
            }
        }
        this.$update();
    }
    async getBlob() {
        let db = this.db = FX.fxBase.dbLocal,
            _id = FX.fxBase.idInfo,
            doc;
        try { doc = await db.getAttachment(_id, this.cell.ulid) } catch (error) { }
        if (!doc) {
            _id = this._idFile = this.cell?._idFile || 'file:' + FX.fxBase.bsSelected._id + '-' + this.cell.ulid;
            try { doc = await db.getAttachment(_id, 'file') } catch (error) { }
        }
        return doc;
    }
    async deleteFile(deleteAttachment = false) {
        if (this.cell) {
            this.cell.url = this.cell.size = this.cell.source = this.cell.ext = '';
            this.cell.attachment = false;
            if (deleteAttachment) {
                FX.fxBase.toDeleteAttachments ||= [];
                FX.fxBase.toDeleteAttachments.push({ _id: fxBase, ulid: this.cell.ulid, _rev: null });
                delete FX.fxBase.notebook._attachments?.[this.cell.ulid];
            }
            delete this.cell.url;
            delete this.cell.ext;
            delete this.cell.source;
            delete this.cell._idFile;
            delete this.cell.size;
            delete this.cell.attachment;
        }
        this.src = this.ext = this.checkedURL = this.url = '';
        this.setIframe();
    }
    
    _setIframe() {
        FX.debounce('setIframe_jsd0c', () => {
            this.setIframe();
        }, 500)
    }
    setIframe(isReady = this.isReady) {
        if (!isReady) return;
        const iframe = this.iframe;
        if (!iframe) return;
        try {
            iframe.addEventListener('load', () => {
                try {
                    if (iframe.contentDocument)
                        iframe.contentDocument.addEventListener("change", this._handleChange);
                } catch (error) { }
            })
            const srcdoc = this.srcdoc(this.checkedURL || '');
            iframe.src = URL.createObjectURL(new Blob([srcdoc], { type: 'text/html' }));
            this.$update();   
        } catch (error) { }
    }

    render() {
        return html`
            <iframe style="border: none; width: 100%; height: 100%;"></iframe>
        `
    }

    srcdoc(src = this.src, editMode = this.editMode) {
        return `
<!DOCTYPE html>
<html lang="en">
<head>
  <link rel="stylesheet" href="${libPath}style.css">
  <script type="module" src="${libPath}superdoc.umd.js"></script>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SuperDoc Editor</title>
  <style>
    body {
        margin: 0;
        padding: 0;
        display: flex;
        flex-direction: column;
        height: 100vh;
        background: white;
    }
    .superdoc__layers.layers {
        margin: 8px auto;
    }
  </style>
</head>
<body>
  <div id="my-toolbar" style="border-bottom: 1px solid lightgray;"></div>
  <div style="overflow: auto;">
        <div id="superdoc"></div>
  </div>

<script type="module">
    let superdoc = null;
    let isReady = false;
    let editor;

    const config = {
        selector: '#superdoc',
        toolbar: '${ editMode ? '#my-toolbar' : '' }',
        document: '${src}',
        documentMode: '${ editMode ? 'editing' : 'viewing' }',
        pagination: true,
        rulers: ${ editMode ? true : false },
        onReady: (event) => {
            isReady = true;
        },
        onEditorCreate: (event) => {
            editor = event.editor;
        },
        onEditorUpdate: async (event) => {
            const docx = await event.editor.exportDocx({ getUpdatedDocs: true });
            document.dispatchEvent(new CustomEvent('change', { detail: docx }));
        }
    }
    superdoc = new SuperDocLibrary.SuperDoc(config);

</script>

</body>
</html>
        `
    }
})
